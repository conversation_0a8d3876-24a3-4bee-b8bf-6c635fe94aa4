defmodule Drops.Relation.Plugins.Queryable.Operations.Order.FieldsCompiler do
  alias Drops.Relation.Schema

  import Ecto.Query

  @spec visit(Ecto.Queryable.t(), map()) :: Ecto.Query.t()
  def visit(queryable, %{opts: opts, schema: schema}) when is_list(opts) do
    order_value = Keyword.get(opts, :order)

    if order_value do
      valid_order_fields = visit(order_value, %{schema: schema, filter: :valid_fields})
      apply_order_by(queryable, valid_order_fields)
    else
      queryable
    end
  end

  def visit(order_value, %{schema: schema, filter: :valid_fields}) do
    field_names = visit(schema, %{extract: :field_names})

    case order_value do
      field when is_atom(field) ->
        if field in field_names, do: field, else: nil

      fields when is_list(fields) ->
        Enum.filter(fields, fn
          field when is_atom(field) ->
            field in field_names

          {direction, field} when direction in [:asc, :desc] and is_atom(field) ->
            field in field_names

          _ ->
            false
        end)

      _ ->
        nil
    end
  end

  def visit(%Schema{fields: fields}, %{extract: :field_names}) do
    Enum.map(fields, &visit(&1, %{extract: :name}))
  end

  def visit(%Schema.Field{name: name}, %{extract: :name}), do: name

  defp apply_order_by(queryable, nil), do: queryable
  defp apply_order_by(queryable, []), do: queryable

  defp apply_order_by(queryable, order_by) when is_atom(order_by) do
    order_by(queryable, ^order_by)
  end

  defp apply_order_by(queryable, order_by) when is_list(order_by) do
    Enum.reduce(order_by, queryable, fn order_spec, query ->
      case order_spec do
        field when is_atom(field) ->
          order_by(query, ^field)

        {direction, field} when direction in [:asc, :desc] and is_atom(field) ->
          order_by(query, [{^direction, ^field}])

        _ ->
          query
      end
    end)
  end
end
